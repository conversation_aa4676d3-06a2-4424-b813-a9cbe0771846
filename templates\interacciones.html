<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestión de Interacciones Ecológicas - VerdeQR</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/gestion.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/common_header.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="gestion-body">
    {% include 'flash_messages.html' %}
    <header class="header-principal">
        <div class="logo-container">
            <img src="{{ url_for('static', filename='css/js/img/logo.png') }}" alt="Logo VerdeQR" class="logo" style="border-radius: 50%; width: 60px; height: 60px; object-fit: cover;">
        </div>
        <nav class="menu-principal">
            <ul id="menu">
                <li><a href="{{ url_for('gestion') }}"><i class="fas fa-home"></i><span>Inicio</span></a></li>
                <li><a href="{{ url_for('arbol') }}"><i class="fas fa-tree"></i><span>Árboles</span></a></li>
                <li><a href="{{ url_for('centro') }}"><i class="fas fa-university"></i><span>Centros</span></a></li>
                <li><a href="{{ url_for('especie') }}"><i class="fas fa-leaf"></i><span>Especies</span></a></li>
                <li><a href="{{ url_for('uso_arbol') }}"><i class="fas fa-tags"></i><span>Usos</span></a></li>
                <li><a href="{{ url_for('tipo_bosque') }}"><i class="fas fa-mountain"></i><span>Tipos de Bosque</span></a></li>
                <li><a href="{{ url_for('curiosidades') }}"><i class="fas fa-lightbulb"></i><span>Curiosidades</span></a></li>
                <li><a href="{{ url_for('interacciones') }}" class="active"><i class="fas fa-network-wired"></i><span>Interacciones Ecológicas</span></a></li>
                <li><a href="{{ url_for('qr') }}"><i class="fas fa-qrcode"></i><span>QR</span></a></li>
                <li><a href="{{ url_for('sugerencias') }}"><i class="fas fa-comments"></i><span>Sugerencias</span></a></li>
                <li><a href="{{ url_for('gestion_usuarios') }}"><i class="fas fa-users"></i><span>Usuarios</span></a></li>
            </ul>
        </nav>
        <div class="user-section">
            <a href="{{ url_for('perfil') }}" class="user-profile-link">
                {% if session['usuario'].get('Imagen') %}
                    <img src="{{ url_for('static', filename=session['usuario']['Imagen']) }}" alt="Usuario" class="user-avatar">
                {% else %}
                    {% if determinar_genero(session['usuario']['Nombres']) == 'femenino' %}
                        <img src="{{ url_for('static', filename='css/js/img/avatarf.jpg') }}" alt="Usuario" class="user-avatar">
                    {% else %}
                        <img src="{{ url_for('static', filename='css/js/img/avatarm.jpg') }}" alt="Usuario" class="user-avatar">
                    {% endif %}
                {% endif %}
                <div class="user-info">
                    <span class="user-name">{{ session['usuario']['Nombre'] }}</span>
                    <span class="user-email">{{ session['usuario']['Correo'] }}</span>
                </div>
            </a>
        </div>
    </header>

    <main class="container">
        <div class="contenedor-seccion">
            <h2>Registro de Interacciones Ecológicas</h2>
            <form method="POST" action="{{ url_for('interacciones') }}" class="formulario-gestion">
                <div class="form-group">
                    <label for="especie">Especie:</label>
                    <select id="especie" name="especie" class="form-control" required>
                        {% for esp in especies %}
                        <option value="{{ esp.IDEspecie }}">{{ esp.NombreCientifico }} ({{ esp.NombreVulgar }})</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="form-group">
                    <label for="tipo_interaccion">Tipo de Interacción:</label>
                    <select id="tipo_interaccion" name="tipo_interaccion" class="form-control" required>
                        <option value="Polinización">Polinización</option>
                        <option value="Dispersión de semillas">Dispersión de semillas</option>
                        <option value="Simbiosis">Simbiosis</option>
                        <option value="Parasitismo">Parasitismo</option>
                        <option value="Mutualismo">Mutualismo</option>
                        <option value="Comensalismo">Comensalismo</option>
                        <option value="Competencia">Competencia</option>
                        <option value="Depredación">Depredación</option>
                        <option value="Otro">Otro</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="descripcion">Descripción:</label>
                    <textarea id="descripcion" name="descripcion" class="form-control" rows="4" required></textarea>
                </div>
                <div class="form-group">
                    <label for="estado">Estado:</label>
                    <select id="estado" name="estado" class="form-control" required>
                        {% for estado in estados %}
                        <option value="{{ estado.IDEstado }}">{{ estado.NombreEstado }}</option>
                        {% endfor %}
                    </select>
                </div>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> Registrar Interacción
                </button>
            </form>
        </div>

        <div class="contenedor-tabla">
            <h2>Interacciones Ecológicas Registradas</h2>
            <div class="table-container">
                <table class="table-gestion">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Especie</th>
                            <th>Tipo de Interacción</th>
                            <th>Descripción</th>
                            <th>Estado</th>
                            <th>Acciones</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for interaccion in interacciones %}
                        <tr>
                            <td>{{ interaccion.IDInteraccion }}</td>
                            <td>{{ interaccion.EspecieNombre }}</td>
                            <td>{{ interaccion.TipoInteraccion }}</td>
                            <td>{{ interaccion.Descripcion }}</td>
                            <td>
                                <span class="badge {% if interaccion.Estado == 1 %}badge-success{% else %}badge-danger{% endif %}">
                                    {{ interaccion.EstadoNombre }}
                                </span>
                            </td>
                            <td class="acciones-cell">
                                <div class="acciones-botones">
                                    <a href="{{ url_for('editar_interaccion', id=interaccion.IDInteraccion) }}" class="btn btn-primary btn-sm">
                                        <i class="fas fa-edit"></i> Editar
                                    </a>
                                    <a href="{{ url_for('eliminar_interaccion', id=interaccion.IDInteraccion) }}" class="btn btn-danger btn-sm" onclick="return confirm('¿Estás seguro de eliminar esta interacción ecológica?');">
                                        <i class="fas fa-trash"></i> Eliminar
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </main>

    <footer class="footer">
        <div class="footer-content">
            <div class="footer-section">
                <h3>VerdeQR</h3>
                <p>Sistema de gestión de árboles y códigos QR</p>
            </div>
            <div class="footer-section">
                <h3>Enlaces</h3>
                <ul>
                    <li><a href="{{ url_for('principal') }}">Inicio</a></li>
                    <li><a href="{{ url_for('contacto') }}">Contacto</a></li>
                    <li><a href="{{ url_for('acerca_de') }}">Acerca de</a></li>
                </ul>
            </div>
            <div class="footer-section">
                <h3>Contacto</h3>
                <p>Email: <EMAIL></p>
                <p>Teléfono: +************</p>
            </div>
        </div>
        <div class="footer-bottom">
            <p>&copy; 2023 VerdeQR. Todos los derechos reservados.</p>
        </div>
    </footer>
</body>
</html>
